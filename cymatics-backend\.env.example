# Environment
NODE_ENV=development
PORT=3000

# Database
DATABASE_URL="postgresql://postgres:darkside@localhost:5433/postgres"

# JWT
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=vbjf yidq fwnl gsfm
FROM_EMAIL=<EMAIL>

# Google Maps
GOOGLE_MAPS_API_KEY=AIzaSyAqIalCM0rqsXeHiyRP4ImBbVgqwMSv8D8

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# OTP Configuration
OTP_EXPIRES_IN_MINUTES=10
OTP_MAX_ATTEMPTS=3

# Session Configuration
SESSION_SECRET=your-session-secret-change-this-in-production
SESSION_MAX_AGE=604800000

# Redis Configuration (for session storage)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# CORS
CORS_ORIGIN=http://localhost:3000,http://localhost:19006

# Security
BCRYPT_ROUNDS=12
